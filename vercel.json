{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install --legacy-peer-deps", "devCommand": "npm run dev", "functions": {"src/app/api/auth/route.ts": {"maxDuration": 30}}, "rewrites": [{"source": "/admin", "destination": "/admin/index.html"}, {"source": "/config.yml", "destination": "/admin/config.yml"}], "headers": [{"source": "/admin/(.*)", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}]}]}