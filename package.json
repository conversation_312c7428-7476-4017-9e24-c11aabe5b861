{"name": "decapcms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test-config": "node scripts/test-config.js"}, "dependencies": {"decap-cms-app": "^3.6.4", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}